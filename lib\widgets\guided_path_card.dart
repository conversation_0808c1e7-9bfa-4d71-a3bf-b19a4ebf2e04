import 'package:flutter/material.dart';
import '../models/models.dart';

class GuidedPathCard extends StatelessWidget {
  final GuidedPath guidedPath;
  final UserPathProgress? userProgress;
  final bool isAccessible;
  final VoidCallback? onTap;
  final VoidCallback? onContinue;

  const GuidedPathCard({
    super.key,
    required this.guidedPath,
    this.userProgress,
    required this.isAccessible,
    this.onTap,
    this.onContinue,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;
    
    return Card(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      elevation: 2,
      child: InkWell(
        onTap: isAccessible ? onTap : () => _showPremiumDialog(context),
        borderRadius: BorderRadius.circular(12),
        child: Stack(
          children: [
            Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Header with title and category
                  Row(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              guidedPath.name,
                              style: theme.textTheme.titleLarge?.copyWith(
                                fontWeight: FontWeight.bold,
                                color: isAccessible 
                                  ? colorScheme.onSurface 
                                  : colorScheme.onSurface.withValues(alpha: 0.6),
                              ),
                            ),
                            const SizedBox(height: 4),
                            Container(
                              padding: const EdgeInsets.symmetric(
                                horizontal: 8,
                                vertical: 4,
                              ),
                              decoration: BoxDecoration(
                                color: _getCategoryColor(context),
                                borderRadius: BorderRadius.circular(12),
                              ),
                              child: Text(
                                guidedPath.category,
                                style: theme.textTheme.labelSmall?.copyWith(
                                  color: Colors.white,
                                  fontWeight: FontWeight.w600,
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                      if (!isAccessible) ...[
                        const SizedBox(width: 8),
                        Container(
                          padding: const EdgeInsets.all(8),
                          decoration: BoxDecoration(
                            color: colorScheme.secondary.withValues(alpha: 0.1),
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: Icon(
                            Icons.lock,
                            color: colorScheme.secondary,
                            size: 20,
                          ),
                        ),
                      ],
                    ],
                  ),
                  
                  const SizedBox(height: 12),
                  
                  // Description
                  Text(
                    guidedPath.description,
                    style: theme.textTheme.bodyMedium?.copyWith(
                      color: isAccessible 
                        ? colorScheme.onSurface.withValues(alpha: 0.8)
                        : colorScheme.onSurface.withValues(alpha: 0.5),
                    ),
                    maxLines: 3,
                    overflow: TextOverflow.ellipsis,
                  ),
                  
                  const SizedBox(height: 16),
                  
                  // Progress bar (if user has started)
                  if (userProgress != null) ...[
                    _buildProgressSection(context),
                    const SizedBox(height: 16),
                  ],
                  
                  // Footer with metadata
                  Row(
                    children: [
                      // Step count
                      _buildInfoChip(
                        context,
                        Icons.list_alt,
                        '${guidedPath.stepCount} steps',
                      ),
                      const SizedBox(width: 8),
                      
                      // Duration
                      if (guidedPath.estimatedCompletionTimeMinutes != null)
                        _buildInfoChip(
                          context,
                          Icons.schedule,
                          _formatDuration(guidedPath.estimatedCompletionTimeMinutes!),
                        ),
                      
                      const SizedBox(width: 8),
                      
                      // Difficulty
                      if (guidedPath.difficultyLevel != null)
                        _buildInfoChip(
                          context,
                          _getDifficultyIcon(guidedPath.difficultyLevel!),
                          guidedPath.difficultyLevel!,
                        ),
                      
                      const Spacer(),
                      
                      // Action button
                      if (isAccessible) _buildActionButton(context),
                    ],
                  ),
                ],
              ),
            ),
            
            // Premium overlay
            if (!isAccessible)
              Positioned.fill(
                child: Container(
                  decoration: BoxDecoration(
                    color: colorScheme.surface.withValues(alpha: 0.8),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Center(
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Icon(
                          Icons.lock,
                          size: 32,
                          color: colorScheme.secondary,
                        ),
                        const SizedBox(height: 8),
                        Text(
                          'Premium Only',
                          style: theme.textTheme.titleMedium?.copyWith(
                            color: colorScheme.secondary,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildProgressSection(BuildContext context) {
    if (userProgress == null) return const SizedBox.shrink();
    
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;
    final progress = userProgress!.getProgressPercentage(guidedPath.stepCount);
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              _getProgressText(),
              style: theme.textTheme.labelMedium?.copyWith(
                color: colorScheme.primary,
                fontWeight: FontWeight.w600,
              ),
            ),
            Text(
              '${(progress * 100).round()}%',
              style: theme.textTheme.labelMedium?.copyWith(
                color: colorScheme.primary,
                fontWeight: FontWeight.w600,
              ),
            ),
          ],
        ),
        const SizedBox(height: 4),
        LinearProgressIndicator(
          value: progress,
          backgroundColor: colorScheme.primary.withValues(alpha: 0.2),
          valueColor: AlwaysStoppedAnimation<Color>(colorScheme.primary),
        ),
      ],
    );
  }

  Widget _buildInfoChip(BuildContext context, IconData icon, String label) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;
    
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: colorScheme.surfaceContainerHighest,
        borderRadius: BorderRadius.circular(8),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            icon,
            size: 14,
            color: colorScheme.onSurface.withValues(alpha: 0.7),
          ),
          const SizedBox(width: 4),
          Text(
            label,
            style: theme.textTheme.labelSmall?.copyWith(
              color: colorScheme.onSurface.withValues(alpha: 0.7),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildActionButton(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;
    
    if (userProgress?.isCompleted == true) {
      return Icon(
        Icons.check_circle,
        color: Colors.green,
        size: 24,
      );
    }
    
    if (userProgress?.isInProgress == true) {
      return TextButton(
        onPressed: onContinue,
        style: TextButton.styleFrom(
          foregroundColor: colorScheme.primary,
          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
        ),
        child: const Text('Continue'),
      );
    }
    
    return Icon(
      Icons.chevron_right,
      color: colorScheme.onSurface.withValues(alpha: 0.6),
    );
  }

  Color _getCategoryColor(BuildContext context) {
    switch (guidedPath.category) {
      case 'Focus & Productivity':
        return Colors.blue;
      case 'Mindset & Resilience':
        return Colors.green;
      case 'Habit Formation':
        return Colors.orange;
      case 'Life Design':
        return Colors.purple;
      default:
        return Theme.of(context).colorScheme.primary;
    }
  }

  IconData _getDifficultyIcon(String difficulty) {
    switch (difficulty.toLowerCase()) {
      case 'beginner':
        return Icons.star_border;
      case 'intermediate':
        return Icons.star_half;
      case 'advanced':
        return Icons.star;
      default:
        return Icons.help_outline;
    }
  }

  String _formatDuration(int minutes) {
    if (minutes < 60) {
      return '${minutes}m';
    } else {
      final hours = minutes ~/ 60;
      final remainingMinutes = minutes % 60;
      if (remainingMinutes == 0) {
        return '${hours}h';
      } else {
        return '${hours}h ${remainingMinutes}m';
      }
    }
  }

  String _getProgressText() {
    if (userProgress == null) return '';
    
    switch (userProgress!.status) {
      case 'completed':
        return 'Completed';
      case 'in_progress':
        return 'In Progress';
      case 'paused':
        return 'Paused';
      default:
        return 'Not Started';
    }
  }

  void _showPremiumDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Premium Feature'),
        content: const Text(
          'This guided path is available for premium users only. '
          'Upgrade your subscription to access all coaching paths and unlock your full potential.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Maybe Later'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              // TODO: Navigate to subscription/upgrade page
            },
            child: const Text('Upgrade Now'),
          ),
        ],
      ),
    );
  }
}
