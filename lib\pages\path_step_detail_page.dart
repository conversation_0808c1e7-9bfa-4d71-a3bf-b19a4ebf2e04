import 'package:flutter/material.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:url_launcher/url_launcher.dart';
import '../models/models.dart' as models;
import '../services/firestore.dart';

class PathStepDetailPage extends StatefulWidget {
  final models.PathStep pathStep;
  final models.GuidedPath guidedPath;
  final models.UserPathProgress? userProgress;

  const PathStepDetailPage({
    super.key,
    required this.pathStep,
    required this.guidedPath,
    this.userProgress,
  });

  @override
  State<PathStepDetailPage> createState() => _PathStepDetailPageState();
}

class _PathStepDetailPageState extends State<PathStepDetailPage> {
  bool _isCompleting = false;
  models.User? _currentUser;
  bool get _isCompleted =>
      widget.userProgress?.isStepCompleted(widget.pathStep.stepNumber) ?? false;
  bool get _isCurrentStep =>
      widget.userProgress?.currentStepNumber == widget.pathStep.stepNumber;

  @override
  void initState() {
    super.initState();
    _loadCurrentUser();
  }

  Future<void> _loadCurrentUser() async {
    try {
      final currentUser = FirebaseAuth.instance.currentUser;
      if (currentUser != null) {
        final user = await FirestoreService.getUser(currentUser.uid);
        setState(() {
          _currentUser = user;
        });
      }
    } catch (e) {
      // Handle error silently, user will just not have admin privileges
    }
  }

  /// Determines if the current user can access this step
  bool get _canAccessStep {
    // Admin users can access any step
    if (_currentUser?.isAdmin == true) {
      return true;
    }

    // If no progress exists, only the first step is accessible
    if (widget.userProgress == null) {
      return widget.pathStep.stepNumber == 1;
    }

    // If step is already completed, it's accessible
    if (_isCompleted) {
      return true;
    }

    // If it's the current step, it's accessible
    if (_isCurrentStep) {
      return true;
    }

    // Otherwise, it's not accessible
    return false;
  }

  Future<void> _completeStep() async {
    if (_isCompleted) return;

    setState(() {
      _isCompleting = true;
    });

    try {
      final currentUser = FirebaseAuth.instance.currentUser;
      if (currentUser == null) return;

      await FirestoreService.completeUserPathStep(
        currentUser.uid,
        widget.guidedPath.id!,
        widget.pathStep.stepNumber,
      );

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Step ${widget.pathStep.stepNumber} completed!'),
            backgroundColor: Colors.green,
          ),
        );
        Navigator.of(context).pop(); // Return to path detail
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to complete step: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      setState(() {
        _isCompleting = false;
      });
    }
  }

  Future<void> _launchUrl(String url) async {
    try {
      final uri = Uri.parse(url);
      if (await canLaunchUrl(uri)) {
        await launchUrl(uri, mode: LaunchMode.externalApplication);
      } else {
        if (mounted) {
          ScaffoldMessenger.of(
            context,
          ).showSnackBar(SnackBar(content: Text('Could not launch $url')));
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(SnackBar(content: Text('Error launching URL: $e')));
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('Step ${widget.pathStep.stepNumber}'),
        actions: [
          if (_isCompleted) Icon(Icons.check_circle, color: Colors.green),
        ],
      ),
      body: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildHeader(),
            _buildContent(),
            _buildCompletionCriteria(),
            if (widget.pathStep.resources?.isNotEmpty == true)
              _buildResources(),
            if (widget.pathStep.reflectionPrompts?.isNotEmpty == true)
              _buildReflectionPrompts(),
            const SizedBox(height: 100), // Space for bottom bar
          ],
        ),
      ),
      bottomNavigationBar: _buildBottomBar(),
    );
  }

  Widget _buildHeader() {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return Container(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Step indicator and path info
          Row(
            children: [
              Container(
                padding: const EdgeInsets.symmetric(
                  horizontal: 12,
                  vertical: 6,
                ),
                decoration: BoxDecoration(
                  color: _getCategoryColor(),
                  borderRadius: BorderRadius.circular(16),
                ),
                child: Text(
                  widget.guidedPath.category,
                  style: theme.textTheme.labelMedium?.copyWith(
                    color: Colors.white,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
              const SizedBox(width: 8),
              Text(
                '${widget.pathStep.stepNumber} of ${widget.guidedPath.stepCount}',
                style: theme.textTheme.labelLarge?.copyWith(
                  color: colorScheme.onSurface.withValues(alpha: 0.7),
                ),
              ),
            ],
          ),

          const SizedBox(height: 12),

          // Title
          Text(
            widget.pathStep.title,
            style: theme.textTheme.headlineMedium?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),

          const SizedBox(height: 8),

          // Duration
          if (widget.pathStep.estimatedDurationMinutes != null)
            Row(
              children: [
                Icon(
                  Icons.schedule,
                  size: 16,
                  color: colorScheme.onSurface.withValues(alpha: 0.7),
                ),
                const SizedBox(width: 4),
                Text(
                  'Estimated time: ${widget.pathStep.estimatedDurationMinutes} minutes',
                  style: theme.textTheme.bodyMedium?.copyWith(
                    color: colorScheme.onSurface.withValues(alpha: 0.7),
                  ),
                ),
              ],
            ),
        ],
      ),
    );
  }

  Widget _buildContent() {
    final theme = Theme.of(context);

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Description',
            style: theme.textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8),
          Text(widget.pathStep.description, style: theme.textTheme.bodyLarge),
          const SizedBox(height: 16),
        ],
      ),
    );
  }

  Widget _buildCompletionCriteria() {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: colorScheme.primaryContainer.withValues(alpha: 0.3),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: colorScheme.primary.withValues(alpha: 0.3)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(Icons.task_alt, color: colorScheme.primary, size: 20),
              const SizedBox(width: 8),
              Text(
                'Completion Criteria',
                style: theme.textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                  color: colorScheme.primary,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Text(
            widget.pathStep.completionCriteria,
            style: theme.textTheme.bodyMedium,
          ),
        ],
      ),
    );
  }

  Widget _buildResources() {
    final theme = Theme.of(context);

    return Container(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Resources',
            style: theme.textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 12),
          ...widget.pathStep.resources!.map(
            (resource) => _buildResourceItem(resource),
          ),
        ],
      ),
    );
  }

  Widget _buildResourceItem(String resource) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;
    final isUrl = resource.startsWith('http');

    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      child: InkWell(
        onTap: isUrl ? () => _launchUrl(resource) : null,
        borderRadius: BorderRadius.circular(8),
        child: Container(
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: colorScheme.surfaceContainerHighest.withValues(alpha: 0.5),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Row(
            children: [
              Icon(
                isUrl ? Icons.link : Icons.description,
                size: 20,
                color: isUrl
                    ? colorScheme.primary
                    : colorScheme.onSurface.withValues(alpha: 0.7),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Text(
                  resource,
                  style: theme.textTheme.bodyMedium?.copyWith(
                    color: isUrl ? colorScheme.primary : null,
                    decoration: isUrl ? TextDecoration.underline : null,
                  ),
                ),
              ),
              if (isUrl)
                Icon(Icons.open_in_new, size: 16, color: colorScheme.primary),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildReflectionPrompts() {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return Container(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(Icons.psychology, color: colorScheme.secondary, size: 20),
              const SizedBox(width: 8),
              Text(
                'Reflection Prompts',
                style: theme.textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          ...widget.pathStep.reflectionPrompts!.asMap().entries.map(
            (entry) => _buildReflectionPrompt(entry.key + 1, entry.value),
          ),
        ],
      ),
    );
  }

  Widget _buildReflectionPrompt(int index, String prompt) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: colorScheme.secondaryContainer.withValues(alpha: 0.3),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            width: 24,
            height: 24,
            decoration: BoxDecoration(
              color: colorScheme.secondary,
              shape: BoxShape.circle,
            ),
            child: Center(
              child: Text(
                '$index',
                style: theme.textTheme.labelSmall?.copyWith(
                  color: colorScheme.onSecondary,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Text(
              prompt,
              style: theme.textTheme.bodyMedium?.copyWith(
                fontStyle: FontStyle.italic,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildBottomBar() {
    final theme = Theme.of(context);

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: theme.colorScheme.surface,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 8,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      child: SafeArea(child: _buildActionButton()),
    );
  }

  Widget _buildActionButton() {
    if (_isCompleted) {
      return SizedBox(
        width: double.infinity,
        child: ElevatedButton.icon(
          onPressed: null,
          icon: const Icon(Icons.check_circle),
          label: const Text('Step Completed'),
          style: ElevatedButton.styleFrom(
            padding: const EdgeInsets.symmetric(vertical: 16),
            backgroundColor: Colors.green,
            foregroundColor: Colors.white,
          ),
        ),
      );
    }

    if (!_canAccessStep) {
      return SizedBox(
        width: double.infinity,
        child: ElevatedButton(
          onPressed: null,
          style: ElevatedButton.styleFrom(
            padding: const EdgeInsets.symmetric(vertical: 16),
          ),
          child: const Text('Complete previous steps first'),
        ),
      );
    }

    return SizedBox(
      width: double.infinity,
      child: ElevatedButton(
        onPressed: _isCompleting ? null : _completeStep,
        style: ElevatedButton.styleFrom(
          padding: const EdgeInsets.symmetric(vertical: 16),
        ),
        child: _isCompleting
            ? const Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  SizedBox(
                    width: 16,
                    height: 16,
                    child: CircularProgressIndicator(strokeWidth: 2),
                  ),
                  SizedBox(width: 8),
                  Text('Completing...'),
                ],
              )
            : const Text('Mark as Complete'),
      ),
    );
  }

  Color _getCategoryColor() {
    switch (widget.guidedPath.category) {
      case 'Focus & Productivity':
        return Colors.blue;
      case 'Mindset & Resilience':
        return Colors.green;
      case 'Habit Formation':
        return Colors.orange;
      case 'Life Design':
        return Colors.purple;
      default:
        return Theme.of(context).colorScheme.primary;
    }
  }
}
